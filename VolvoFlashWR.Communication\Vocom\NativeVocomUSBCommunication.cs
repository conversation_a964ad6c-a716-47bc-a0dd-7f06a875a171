using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Text;
using Microsoft.Win32.SafeHandles;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Communication.Interfaces;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Native USB communication service for direct Vocom adapter communication
    /// Uses Windows USB APIs for low-level hardware access
    /// </summary>
    public class NativeVocomUSBCommunication : IUSBCommunicationService
    {
        private readonly ILoggingService _logger;
        private bool _isConnected = false;
        private SafeFileHandle? _deviceHandle;
        private string? _connectedDevicePath;

        // Windows USB API constants
        private const uint GENERIC_READ = 0x80000000;
        private const uint GENERIC_WRITE = 0x40000000;
        private const uint FILE_SHARE_READ = 0x00000001;
        private const uint FILE_SHARE_WRITE = 0x00000002;
        private const uint OPEN_EXISTING = 3;
        private const uint FILE_ATTRIBUTE_NORMAL = 0x80;
        private const uint FILE_FLAG_OVERLAPPED = 0x40000000;

        // Vocom device identifiers
        private const ushort VOCOM_VENDOR_ID = 0x178E;
        private const ushort VOCOM_PRODUCT_ID = 0x0024;
        private const string VOCOM_DEVICE_NAME = "88890300";

        // Windows API imports
        [DllImport("kernel32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern SafeFileHandle CreateFile(
            string lpFileName,
            uint dwDesiredAccess,
            uint dwShareMode,
            IntPtr lpSecurityAttributes,
            uint dwCreationDisposition,
            uint dwFlagsAndAttributes,
            IntPtr hTemplateFile);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool ReadFile(
            SafeFileHandle hFile,
            byte[] lpBuffer,
            uint nNumberOfBytesToRead,
            out uint lpNumberOfBytesRead,
            IntPtr lpOverlapped);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool WriteFile(
            SafeFileHandle hFile,
            byte[] lpBuffer,
            uint nNumberOfBytesToWrite,
            out uint lpNumberOfBytesWritten,
            IntPtr lpOverlapped);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern IntPtr SetupDiGetClassDevs(
            ref Guid classGuid,
            string? enumerator,
            IntPtr hwndParent,
            uint flags);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiEnumDeviceInfo(
            IntPtr deviceInfoSet,
            uint memberIndex,
            ref SP_DEVINFO_DATA deviceInfoData);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiGetDeviceInstanceId(
            IntPtr deviceInfoSet,
            ref SP_DEVINFO_DATA deviceInfoData,
            StringBuilder deviceInstanceId,
            uint deviceInstanceIdSize,
            out uint requiredSize);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiDestroyDeviceInfoList(IntPtr deviceInfoSet);

        [StructLayout(LayoutKind.Sequential)]
        private struct SP_DEVINFO_DATA
        {
            public uint cbSize;
            public Guid classGuid;
            public uint devInst;
            public IntPtr reserved;
        }

        // HID class GUID
        private static readonly Guid HID_CLASS_GUID = new Guid("4d1e55b2-f16f-11cf-88cb-001111000030");

        public NativeVocomUSBCommunication(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public bool IsConnected => _isConnected;

        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing native Vocom USB communication", "NativeVocomUSBCommunication");
                
                // Check if we can access USB devices
                var devices = await DetectVocomDevicesAsync();
                
                _logger.LogInformation($"Found {devices.Count} potential Vocom devices", "NativeVocomUSBCommunication");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to initialize native USB communication", "NativeVocomUSBCommunication", ex);
                return false;
            }
        }

        public async Task<List<string>> DetectVocomDevicesAsync()
        {
            var devices = new List<string>();

            try
            {
                _logger.LogInformation("Detecting Vocom devices using native USB enumeration", "NativeVocomUSBCommunication");

                // Get all HID devices
                IntPtr deviceInfoSet = SetupDiGetClassDevs(
                    ref HID_CLASS_GUID,
                    null,
                    IntPtr.Zero,
                    0x00000010 | 0x00000002); // DIGCF_PRESENT | DIGCF_DEVICEINTERFACE

                if (deviceInfoSet == IntPtr.Zero || deviceInfoSet == new IntPtr(-1))
                {
                    _logger.LogWarning("Failed to get device information set", "NativeVocomUSBCommunication");
                    return devices;
                }

                try
                {
                    uint deviceIndex = 0;
                    var deviceInfoData = new SP_DEVINFO_DATA();
                    deviceInfoData.cbSize = (uint)Marshal.SizeOf(deviceInfoData);

                    while (SetupDiEnumDeviceInfo(deviceInfoSet, deviceIndex, ref deviceInfoData))
                    {
                        var deviceInstanceId = new StringBuilder(256);
                        if (SetupDiGetDeviceInstanceId(deviceInfoSet, ref deviceInfoData, deviceInstanceId, 256, out _))
                        {
                            string deviceId = deviceInstanceId.ToString();
                            
                            // Check if this is a Vocom device
                            if (IsVocomDevice(deviceId))
                            {
                                _logger.LogInformation($"Found Vocom device: {deviceId}", "NativeVocomUSBCommunication");
                                devices.Add(deviceId);
                            }
                        }

                        deviceIndex++;
                    }
                }
                finally
                {
                    SetupDiDestroyDeviceInfoList(deviceInfoSet);
                }

                _logger.LogInformation($"Native USB detection found {devices.Count} Vocom devices", "NativeVocomUSBCommunication");
            }
            catch (Exception ex)
            {
                _logger.LogError("Error during native USB device detection", "NativeVocomUSBCommunication", ex);
            }

            await Task.CompletedTask;
            return devices;
        }

        private bool IsVocomDevice(string deviceId)
        {
            // Check for Vocom vendor and product IDs
            return deviceId.Contains($"VID_{VOCOM_VENDOR_ID:X4}", StringComparison.OrdinalIgnoreCase) &&
                   deviceId.Contains($"PID_{VOCOM_PRODUCT_ID:X4}", StringComparison.OrdinalIgnoreCase);
        }

        public async Task<bool> ConnectToDeviceAsync(string portName, int baudRate = 115200, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Attempting native USB connection to: {portName}", "NativeVocomUSBCommunication");

                // If already connected, disconnect first
                if (_isConnected)
                {
                    await DisconnectAsync();
                }

                // Try to open the device
                string devicePath = GetDevicePath(portName);
                if (string.IsNullOrEmpty(devicePath))
                {
                    _logger.LogWarning($"Could not determine device path for: {portName}", "NativeVocomUSBCommunication");
                    return false;
                }

                _deviceHandle = CreateFile(
                    devicePath,
                    GENERIC_READ | GENERIC_WRITE,
                    FILE_SHARE_READ | FILE_SHARE_WRITE,
                    IntPtr.Zero,
                    OPEN_EXISTING,
                    FILE_ATTRIBUTE_NORMAL,
                    IntPtr.Zero);

                if (_deviceHandle.IsInvalid)
                {
                    int error = Marshal.GetLastWin32Error();
                    _logger.LogError($"Failed to open device {devicePath}. Error: {error}", "NativeVocomUSBCommunication");
                    return false;
                }

                _connectedDevicePath = devicePath;
                _isConnected = true;

                _logger.LogInformation($"Successfully connected to Vocom device: {devicePath}", "NativeVocomUSBCommunication");
                USBConnected?.Invoke(this, portName);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error connecting to device {portName}", "NativeVocomUSBCommunication", ex);
                USBError?.Invoke(this, $"Connection error: {ex.Message}");
                return false;
            }
        }

        private string GetDevicePath(string portName)
        {
            // Convert port name to device path
            // This is a simplified implementation - in practice, you'd need to
            // enumerate device interfaces to get the actual path
            if (portName.StartsWith("USB\\"))
            {
                return $"\\\\.\\{portName}";
            }
            
            return $"\\\\.\\{portName}";
        }

        public async Task<bool> DisconnectAsync()
        {
            try
            {
                if (_deviceHandle != null && !_deviceHandle.IsInvalid)
                {
                    _deviceHandle.Dispose();
                    _deviceHandle = null;
                }

                _isConnected = false;
                _connectedDevicePath = null;

                _logger.LogInformation("Disconnected from Vocom device", "NativeVocomUSBCommunication");
                USBDisconnected?.Invoke(this, "Device disconnected");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error during disconnect", "NativeVocomUSBCommunication", ex);
                return false;
            }
            finally
            {
                await Task.CompletedTask;
            }
        }

        public async Task<byte[]?> SendAndReceiveDataAsync(byte[] data, int expectedResponseLength, int timeoutMs = 5000)
        {
            if (!_isConnected || _deviceHandle == null || _deviceHandle.IsInvalid)
            {
                _logger.LogWarning("Cannot send data - device not connected", "NativeVocomUSBCommunication");
                return null;
            }

            try
            {
                _logger.LogDebug($"Sending {data.Length} bytes to Vocom device", "NativeVocomUSBCommunication");

                // Send data
                bool writeResult = WriteFile(_deviceHandle, data, (uint)data.Length, out uint bytesWritten, IntPtr.Zero);
                if (!writeResult || bytesWritten != data.Length)
                {
                    int error = Marshal.GetLastWin32Error();
                    _logger.LogError($"Failed to write data. Error: {error}, Bytes written: {bytesWritten}/{data.Length}", "NativeVocomUSBCommunication");
                    return null;
                }

                // Read response
                byte[] responseBuffer = new byte[expectedResponseLength];
                bool readResult = ReadFile(_deviceHandle, responseBuffer, (uint)expectedResponseLength, out uint bytesRead, IntPtr.Zero);
                
                if (!readResult)
                {
                    int error = Marshal.GetLastWin32Error();
                    _logger.LogError($"Failed to read response. Error: {error}", "NativeVocomUSBCommunication");
                    return null;
                }

                if (bytesRead > 0)
                {
                    byte[] response = new byte[bytesRead];
                    Array.Copy(responseBuffer, response, bytesRead);
                    
                    _logger.LogDebug($"Received {bytesRead} bytes from Vocom device", "NativeVocomUSBCommunication");
                    return response;
                }

                _logger.LogWarning("No response received from Vocom device", "NativeVocomUSBCommunication");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error during data communication", "NativeVocomUSBCommunication", ex);
                return null;
            }
            finally
            {
                await Task.CompletedTask;
            }
        }

        public async Task<bool> IsUSBAvailableAsync()
        {
            try
            {
                var devices = await DetectVocomDevicesAsync();
                return devices.Count > 0;
            }
            catch
            {
                return false;
            }
        }

        // Events
        public event EventHandler<string>? USBConnected;
        public event EventHandler<string>? USBDisconnected;
        public event EventHandler<string>? USBError;

        public void Dispose()
        {
            DisconnectAsync().Wait();
        }
    }
}
