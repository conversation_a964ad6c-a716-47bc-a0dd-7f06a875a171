using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Services;
using VolvoFlashWR.Communication.Vocom;
using VolvoFlashWR.Communication.Models;

namespace VolvoFlashWR.Tests.Integration
{
    /// <summary>
    /// Integration tests for the enhanced Vocom connection system
    /// Tests the complete flow from device detection to successful communication
    /// </summary>
    [TestClass]
    public class VocomConnectionIntegrationTests
    {
        private ILoggingService _logger;
        private VocomService _vocomService;
        private EnhancedVocomDeviceDetection _deviceDetection;
        private VocomConnectionRecoveryService _recoveryService;
        private NativeVocomUSBCommunication _nativeUsbService;

        [TestInitialize]
        public async Task TestInitialize()
        {
            // Initialize logging service
            _logger = new LoggingService();

            // Initialize enhanced device detection
            _deviceDetection = new EnhancedVocomDeviceDetection(_logger);

            // Initialize native USB communication service
            _nativeUsbService = new NativeVocomUSBCommunication(_logger);
            await _nativeUsbService.InitializeAsync();

            // Initialize Vocom service
            _vocomService = new VocomService(_logger);
            await _vocomService.InitializeAsync();

            // Initialize recovery service
            _recoveryService = new VocomConnectionRecoveryService(_logger, _vocomService, _deviceDetection);
        }

        [TestCleanup]
        public async Task TestCleanup()
        {
            if (_vocomService?.IsConnected == true)
            {
                await _vocomService.DisconnectAsync();
            }

            _nativeUsbService?.Dispose();
        }

        /// <summary>
        /// Tests the complete device detection flow with enhanced capabilities
        /// </summary>
        [TestMethod]
        public async Task TestEnhancedDeviceDetection()
        {
            // Arrange
            _logger.LogInformation("Starting enhanced device detection test", "VocomConnectionIntegrationTests");

            // Act
            var devices = await _deviceDetection.DetectVocomDevicesAsync();

            // Assert
            Assert.IsNotNull(devices, "Device detection should return a list");
            _logger.LogInformation($"Detected {devices.Count} Vocom devices", "VocomConnectionIntegrationTests");

            // Verify device properties
            foreach (var device in devices)
            {
                Assert.IsNotNull(device.Name, "Device name should not be null");
                Assert.IsNotNull(device.DeviceId, "Device ID should not be null");
                Assert.IsTrue(device.ConnectionType != VocomConnectionType.Unknown, "Connection type should be specified");
                
                _logger.LogInformation($"Device: {device.Name} ({device.DeviceId}) - {device.ConnectionType}", "VocomConnectionIntegrationTests");
            }
        }

        /// <summary>
        /// Tests architecture-aware library loading
        /// </summary>
        [TestMethod]
        public async Task TestArchitectureAwareLibraryLoading()
        {
            // Arrange
            var dependencyManager = new DependencyManager(_logger);
            _logger.LogInformation("Starting architecture-aware library loading test", "VocomConnectionIntegrationTests");

            // Act
            bool initialized = await dependencyManager.InitializeAsync();

            // Assert
            Assert.IsTrue(initialized, "Dependency manager should initialize successfully");
            _logger.LogInformation("Architecture-aware library loading test completed successfully", "VocomConnectionIntegrationTests");
        }

        /// <summary>
        /// Tests native USB communication capabilities
        /// </summary>
        [TestMethod]
        public async Task TestNativeUSBCommunication()
        {
            // Arrange
            _logger.LogInformation("Starting native USB communication test", "VocomConnectionIntegrationTests");

            // Act
            bool isAvailable = await _nativeUsbService.IsUSBAvailableAsync();
            var devices = await _nativeUsbService.DetectVocomDevicesAsync();

            // Assert
            _logger.LogInformation($"USB available: {isAvailable}, Devices found: {devices.Count}", "VocomConnectionIntegrationTests");
            Assert.IsNotNull(devices, "Device detection should return a list");

            // If devices are found, test basic communication
            if (devices.Count > 0)
            {
                string firstDevice = devices[0];
                _logger.LogInformation($"Testing connection to: {firstDevice}", "VocomConnectionIntegrationTests");

                // Note: This test may fail if no real hardware is connected
                // In a real test environment, this would connect to actual hardware
                bool connected = await _nativeUsbService.ConnectToDeviceAsync(firstDevice);
                _logger.LogInformation($"Connection result: {connected}", "VocomConnectionIntegrationTests");
            }
        }

        /// <summary>
        /// Tests the connection recovery mechanism
        /// </summary>
        [TestMethod]
        public async Task TestConnectionRecovery()
        {
            // Arrange
            _logger.LogInformation("Starting connection recovery test", "VocomConnectionIntegrationTests");
            
            var testDevice = new VocomDevice
            {
                Name = "Test Vocom Device",
                DeviceId = "TEST_DEVICE_ID",
                ConnectionType = VocomConnectionType.USB,
                PortName = "TEST_PORT"
            };

            var testException = new Exception("Simulated connection failure");

            // Act
            bool recovered = await _recoveryService.RecoverConnectionAsync(testDevice, testException);
            var statistics = _recoveryService.GetRecoveryStatistics();

            // Assert
            Assert.IsNotNull(statistics, "Recovery statistics should be available");
            _logger.LogInformation($"Recovery result: {recovered}, Consecutive failures: {statistics.ConsecutiveFailures}", "VocomConnectionIntegrationTests");
        }

        /// <summary>
        /// Tests the complete connection flow with real hardware (if available)
        /// </summary>
        [TestMethod]
        public async Task TestCompleteConnectionFlow()
        {
            // Arrange
            _logger.LogInformation("Starting complete connection flow test", "VocomConnectionIntegrationTests");

            // Act - Scan for devices
            var devices = await _vocomService.ScanForDevicesAsync();
            
            // Assert
            Assert.IsNotNull(devices, "Device scan should return a list");
            _logger.LogInformation($"Found {devices.Count} devices during scan", "VocomConnectionIntegrationTests");

            if (devices.Count > 0)
            {
                var firstDevice = devices[0];
                _logger.LogInformation($"Attempting connection to: {firstDevice.Name}", "VocomConnectionIntegrationTests");

                // Act - Attempt connection
                bool connected = await _vocomService.ConnectAsync(firstDevice);
                
                // Assert
                _logger.LogInformation($"Connection result: {connected}", "VocomConnectionIntegrationTests");

                if (connected)
                {
                    // Test basic communication
                    byte[] testData = { 0x01, 0x02, 0x03 };
                    byte[] response = await _vocomService.SendAndReceiveDataAsync(testData, 100, 5000);
                    
                    Assert.IsNotNull(response, "Should receive a response");
                    _logger.LogInformation($"Communication test: Sent {testData.Length} bytes, received {response.Length} bytes", "VocomConnectionIntegrationTests");

                    // Disconnect
                    bool disconnected = await _vocomService.DisconnectAsync();
                    Assert.IsTrue(disconnected, "Should disconnect successfully");
                    _logger.LogInformation("Disconnection successful", "VocomConnectionIntegrationTests");
                }
            }
            else
            {
                _logger.LogInformation("No devices found - test completed without hardware connection", "VocomConnectionIntegrationTests");
            }
        }

        /// <summary>
        /// Tests MC9S12XEP100 protocol handler functionality
        /// </summary>
        [TestMethod]
        public async Task TestMC9S12XEP100ProtocolHandler()
        {
            // Arrange
            _logger.LogInformation("Starting MC9S12XEP100 protocol handler test", "VocomConnectionIntegrationTests");
            
            var protocolHandler = new MC9S12XEP100ProtocolHandler(_logger, _vocomService, Communication.Models.ECUProtocolType.CAN);

            // Act
            bool initialized = await protocolHandler.InitializeAsync();

            // Assert
            Assert.IsTrue(initialized, "Protocol handler should initialize successfully");
            _logger.LogInformation("MC9S12XEP100 protocol handler initialized successfully", "VocomConnectionIntegrationTests");

            // Test speed change functionality
            bool speedChanged = await protocolHandler.SetCommunicationSpeedAsync(MC9S12XEP100ProtocolHandler.CommunicationSpeed.HighSpeed);
            _logger.LogInformation($"Speed change result: {speedChanged}", "VocomConnectionIntegrationTests");
        }

        /// <summary>
        /// Tests error handling and logging throughout the system
        /// </summary>
        [TestMethod]
        public async Task TestErrorHandlingAndLogging()
        {
            // Arrange
            _logger.LogInformation("Starting error handling and logging test", "VocomConnectionIntegrationTests");
            
            var invalidDevice = new VocomDevice
            {
                Name = "Invalid Device",
                DeviceId = "INVALID_ID",
                ConnectionType = VocomConnectionType.USB,
                PortName = "INVALID_PORT"
            };

            // Act - Attempt connection to invalid device
            bool connected = await _vocomService.ConnectAsync(invalidDevice);

            // Assert
            Assert.IsFalse(connected, "Connection to invalid device should fail");
            _logger.LogInformation("Error handling test completed - invalid connection properly rejected", "VocomConnectionIntegrationTests");

            // Test recovery mechanism with invalid device
            var testException = new Exception("Invalid device connection error");
            bool recovered = await _recoveryService.RecoverConnectionAsync(invalidDevice, testException);
            
            _logger.LogInformation($"Recovery attempt result: {recovered}", "VocomConnectionIntegrationTests");
        }

        /// <summary>
        /// Performance test for device detection and connection
        /// </summary>
        [TestMethod]
        public async Task TestPerformanceMetrics()
        {
            // Arrange
            _logger.LogInformation("Starting performance metrics test", "VocomConnectionIntegrationTests");
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act - Device detection performance
            var devices = await _deviceDetection.DetectVocomDevicesAsync();
            stopwatch.Stop();

            // Assert
            var detectionTime = stopwatch.ElapsedMilliseconds;
            _logger.LogInformation($"Device detection completed in {detectionTime}ms, found {devices.Count} devices", "VocomConnectionIntegrationTests");
            
            Assert.IsTrue(detectionTime < 10000, "Device detection should complete within 10 seconds");

            // Test connection performance if devices are available
            if (devices.Count > 0)
            {
                stopwatch.Restart();
                bool connected = await _vocomService.ConnectAsync(devices[0]);
                stopwatch.Stop();

                var connectionTime = stopwatch.ElapsedMilliseconds;
                _logger.LogInformation($"Connection attempt completed in {connectionTime}ms, result: {connected}", "VocomConnectionIntegrationTests");
                
                if (connected)
                {
                    await _vocomService.DisconnectAsync();
                }
            }
        }
    }
}
